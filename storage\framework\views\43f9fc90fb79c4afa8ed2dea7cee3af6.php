<!-- Theme Toggle Switch -->
<div class="flex items-center">
    <button
        wire:click="toggleTheme"
        class="relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-custom-green focus:ring-offset-2 <?php echo e($isDarkMode ? 'bg-custom-green' : 'bg-gray-300'); ?>"
        role="switch"
        aria-checked="<?php echo e($isDarkMode ? 'true' : 'false'); ?>"
        aria-label="Toggle dark mode">

        <!-- Toggle Circle -->
        <span class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 <?php echo e($isDarkMode ? 'translate-x-6' : 'translate-x-1'); ?>"></span>

        <!-- Icons -->
        <div class="absolute inset-0 flex items-center justify-between px-1 pointer-events-none">
            <!-- Sun Icon (Light Mode) -->
            <svg class="h-3 w-3 text-gray-500 <?php echo e($isDarkMode ? 'opacity-0' : 'opacity-100'); ?> transition-opacity duration-200"
                 fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd" />
            </svg>

            <!-- Moon Icon (Dark Mode) -->
            <svg class="h-3 w-3 text-white <?php echo e($isDarkMode ? 'opacity-100' : 'opacity-0'); ?> transition-opacity duration-200"
                 fill="currentColor" viewBox="0 0 20 20">
                <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
            </svg>
        </div>
    </button>

    <!-- Optional Label -->
    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300 hidden sm:inline">
        <?php echo e($isDarkMode ? 'Dark' : 'Light'); ?>

    </span>
</div>

<script>
    // Listen for theme changes and update the document class
    document.addEventListener('livewire:init', () => {
        Livewire.on('theme-changed', (event) => {
            const theme = event.theme;
            if (theme === 'dark') {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        });
    });

    // Set initial theme on page load
    document.addEventListener('DOMContentLoaded', function() {
        <!--[if BLOCK]><![endif]--><?php if(auth()->check() && auth()->user()->prefersDarkMode()): ?>
            document.documentElement.classList.add('dark');
        <?php else: ?>
            document.documentElement.classList.remove('dark');
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    });
</script>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/livewire/theme-toggle.blade.php ENDPATH**/ ?>