<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ThemeController extends Controller
{
    /**
     * Toggle user's theme preference
     */
    public function toggle(Request $request)
    {
        if (!Auth::check()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $user = Auth::user();
        $currentTheme = $user->getThemePreference();
        $newTheme = $currentTheme === 'dark' ? 'light' : 'dark';

        $user->setThemePreference($newTheme);

        return response()->json([
            'success' => true,
            'theme' => $newTheme,
            'message' => 'Theme updated successfully'
        ]);
    }

    /**
     * Set specific theme
     */
    public function set(Request $request)
    {
        $request->validate([
            'theme' => 'required|in:light,dark'
        ]);

        if (!Auth::check()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $user = Auth::user();
        $user->setThemePreference($request->theme);

        return response()->json([
            'success' => true,
            'theme' => $request->theme,
            'message' => 'Theme updated successfully'
        ]);
    }

    /**
     * Get current theme
     */
    public function current()
    {
        if (!Auth::check()) {
            return response()->json(['theme' => 'light']);
        }

        return response()->json([
            'theme' => Auth::user()->getThemePreference()
        ]);
    }
}
