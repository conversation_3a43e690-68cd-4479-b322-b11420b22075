<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'UniLink') }}</title>

        <!-- Preload critical resources -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link rel="dns-prefetch" href="https://fonts.bunny.net">

        <!-- Fonts with display=swap to prevent layout shift -->
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Critical CSS and JS -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])
    </head>
    <body class="font-sans text-custom-darkest dark:text-custom-dark-text antialiased">
        <!-- Background with gradient -->
        <div class="min-h-screen flex flex-col sm:justify-center items-center pt-6 sm:pt-0 bg-gradient-to-br from-custom-lightest via-gray-50 to-custom-lightest dark:from-custom-dark-bg dark:via-custom-dark-surface dark:to-custom-dark-bg">
            <!-- Logo Section -->
            <div class="mb-8">
                <a href="/" class="flex flex-col items-center group">
                    <x-application-logo class="w-16 h-16 fill-current text-custom-green transition-transform duration-300 group-hover:scale-110" />
                    <h1 class="mt-3 text-2xl font-bold text-custom-darkest dark:text-custom-dark-text">UniLink</h1>
                    <p class="text-sm text-custom-dark-gray dark:text-custom-dark-text-secondary mt-1">Connect. Learn. Grow.</p>
                </a>
            </div>

            <!-- Form Container -->
            <div class="w-full sm:max-w-md">
                <div class="bg-white shadow-xl border border-custom-lightest overflow-hidden sm:rounded-2xl">
                    <div class="px-8 py-8">
                        {{ $slot }}
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="mt-8 text-center">
                <p class="text-sm text-custom-dark-gray">
                    © {{ date('Y') }} UniLink. Connecting university communities.
                </p>
            </div>
        </div>

        <!-- Ensure DOM is ready before layout-dependent operations -->
        <script>
            // Prevent layout thrashing by ensuring DOM is ready
            document.addEventListener('DOMContentLoaded', function() {
                // Force a reflow to ensure all styles are applied
                document.body.offsetHeight;

                // Initialize any layout-dependent functionality here
                if (window.initializeLayoutDependentFeatures) {
                    window.initializeLayoutDependentFeatures();
                }
            });
        </script>
    </body>
</html>
