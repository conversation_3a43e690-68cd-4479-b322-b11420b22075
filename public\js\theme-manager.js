/**
 * Theme Manager - Handles dark/light mode switching and persistence
 */
class ThemeManager {
    constructor() {
        this.init();
    }

    init() {
        // Set initial theme on page load
        this.setInitialTheme();
        
        // Listen for theme changes from Livewire component
        this.setupEventListeners();
        
        // Handle system theme changes
        this.setupSystemThemeListener();
    }

    setInitialTheme() {
        // Check if user is authenticated and has a preference
        const userTheme = this.getUserTheme();
        
        if (userTheme) {
            this.applyTheme(userTheme);
        } else {
            // Fall back to system preference for guests
            const systemTheme = this.getSystemTheme();
            this.applyTheme(systemTheme);
        }
    }

    getUserTheme() {
        // This will be set by the server-side template
        const metaTheme = document.querySelector('meta[name="user-theme"]');
        return metaTheme ? metaTheme.getAttribute('content') : null;
    }

    getSystemTheme() {
        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }

    applyTheme(theme) {
        const html = document.documentElement;
        
        if (theme === 'dark') {
            html.classList.add('dark');
        } else {
            html.classList.remove('dark');
        }

        // Store in localStorage for guests
        if (!this.getUserTheme()) {
            localStorage.setItem('theme', theme);
        }

        // Dispatch custom event for other components
        window.dispatchEvent(new CustomEvent('theme-changed', { 
            detail: { theme } 
        }));
    }

    setupEventListeners() {
        // Listen for Livewire theme changes
        document.addEventListener('livewire:init', () => {
            if (window.Livewire) {
                window.Livewire.on('theme-changed', (event) => {
                    this.applyTheme(event.theme);
                });
            }
        });

        // Listen for manual theme toggle (for guests or fallback)
        document.addEventListener('theme-toggle', (event) => {
            const currentTheme = document.documentElement.classList.contains('dark') ? 'dark' : 'light';
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            this.applyTheme(newTheme);
        });
    }

    setupSystemThemeListener() {
        // Listen for system theme changes (only for guests)
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        mediaQuery.addEventListener('change', (e) => {
            // Only apply system theme if user doesn't have a saved preference
            if (!this.getUserTheme() && !localStorage.getItem('theme')) {
                this.applyTheme(e.matches ? 'dark' : 'light');
            }
        });
    }

    // Public method to toggle theme
    toggle() {
        const currentTheme = document.documentElement.classList.contains('dark') ? 'dark' : 'light';
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        
        if (this.getUserTheme()) {
            // User is authenticated, use API
            this.updateUserTheme(newTheme);
        } else {
            // Guest user, use localStorage
            this.applyTheme(newTheme);
        }
    }

    async updateUserTheme(theme) {
        try {
            const response = await fetch('/theme/set', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({ theme })
            });

            const data = await response.json();
            
            if (data.success) {
                this.applyTheme(theme);
            } else {
                console.error('Failed to update theme:', data.error);
            }
        } catch (error) {
            console.error('Error updating theme:', error);
        }
    }
}

// Initialize theme manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.themeManager = new ThemeManager();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThemeManager;
}
