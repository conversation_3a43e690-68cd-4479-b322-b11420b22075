import defaultTheme from 'tailwindcss/defaultTheme';
import forms from '@tailwindcss/forms';

/** @type {import('tailwindcss').Config} */
export default {
    content: [
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
        './storage/framework/views/*.php',
        './resources/views/**/*.blade.php',
    ],

    darkMode: 'class', // Enable class-based dark mode

    theme: {
        extend: {
            fontFamily: {
                sans: ['Figtree', ...defaultTheme.fontFamily.sans],
            },
            colors: {
                'custom': {
                    'lightest': '#EEEEEE',      // Light Gray
                    'green': '#7BC74D',         // Green
                    'dark-gray': '#393E46',     // Dark Gray
                    'darkest': '#222831',       // Dark Navy
                    // Dark mode specific colors
                    'dark': {
                        'bg': '#1a1a1a',        // Main dark background
                        'surface': '#2d2d2d',   // Card/surface background
                        'border': '#404040',    // Border color
                        'text': '#e5e5e5',     // Primary text
                        'text-secondary': '#b3b3b3', // Secondary text
                        'hover': '#3a3a3a',    // Hover states
                    }
                }
            },
        },
    },

    plugins: [forms],
};
