@if($compact)
    <!-- Compact Mode for Suggested Connections -->
    @auth
        @if(auth()->id() !== $user->id)
            <button
                wire:click="toggleFollow"
                wire:loading.attr="disabled"
                class="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full transition-colors disabled:opacity-50 {{ $isFollowing ? 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600' : 'bg-blue-600 text-white hover:bg-blue-700' }}">

                @if($isFollowing)
                    <span wire:loading.remove wire:target="toggleFollow">Following</span>
                @else
                    <span wire:loading.remove wire:target="toggleFollow">Follow</span>
                @endif

                <span wire:loading wire:target="toggleFollow">
                    <svg class="animate-spin w-3 h-3" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </span>
            </button>
        @endif
    @else
        <a href="{{ route('login') }}" class="inline-flex items-center px-3 py-1 text-xs font-medium text-blue-600 bg-blue-50 dark:bg-blue-900 dark:text-blue-300 rounded-full hover:bg-blue-100 dark:hover:bg-blue-800 transition-colors">
            Follow
        </a>
    @endauth
@else
    <!-- Regular Mode -->
    <div class="flex items-center space-x-4">
        @auth
            @if(auth()->id() !== $user->id)
                <button
                    wire:click="toggleFollow"
                    wire:loading.attr="disabled"
                    class="flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 {{ $isFollowing ? 'bg-gray-600 text-white hover:bg-gray-700' : 'bg-blue-600 text-white hover:bg-blue-700' }}">

                    @if($isFollowing)
                        <!-- <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" wire:loading.remove wire:target="toggleFollow">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg> -->
                        <span wire:loading.remove wire:target="toggleFollow">Following</span>
                    @else
                        <!-- <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" wire:loading.remove wire:target="toggleFollow">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg> -->
                        <span wire:loading.remove wire:target="toggleFollow">Follow</span>
                    @endif

                    <span wire:loading wire:target="toggleFollow">
                        <svg class="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        {{ $isFollowing ? 'Unfollowing...' : 'Following...' }}
                    </span>
                </button>
            @endif
        @else
            <a href="{{ route('login') }}" class="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 flex items-center space-x-2">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m-6h6m-6 0H6" />
                </svg>
                <span>Follow</span>
            </a>
        @endauth

        <!-- Followers Count -->
        <!-- <div class="flex items-center text-sm text-gray-500">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            <a href="{{ route('users.followers', $user) }}" class="hover:text-blue-600 transition-colors">
                {{ number_format($followersCount) }} followers
            </a>
        </div> -->
    </div>
@endif
