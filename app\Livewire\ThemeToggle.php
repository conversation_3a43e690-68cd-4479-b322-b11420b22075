<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class ThemeToggle extends Component
{
    public $isDarkMode = false;

    public function mount()
    {
        if (Auth::check()) {
            $this->isDarkMode = Auth::user()->prefersDarkMode();
        }
    }

    public function toggleTheme()
    {
        if (Auth::check()) {
            $newTheme = $this->isDarkMode ? 'light' : 'dark';
            Auth::user()->setThemePreference($newTheme);
            $this->isDarkMode = !$this->isDarkMode;

            // Dispatch browser event to update the DOM class
            $this->dispatch('theme-changed', theme: $newTheme);
        }
    }

    public function render()
    {
        return view('livewire.theme-toggle');
    }
}
